<!doctype html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Live2D Mini - Hiyori</title>
    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        margin: 0;
        padding: 0;
        background: transparent;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        overflow: hidden;
        height: 100vh;
        width: 100vw;
      }

      #canvas {
        display: block;
        width: 100%;
        height: 100%;
        cursor: pointer;
      }

      /* 模型位置过渡动画 */
      .model-transition {
        transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      }

      .status-bar {
        position: absolute;
        bottom: 10px;
        left: 10px;
        right: 10px;
        background: rgba(0, 0, 0, 0.6);
        color: white;
        padding: 8px 12px;
        border-radius: 15px;
        font-size: 12px;
        text-align: center;
        backdrop-filter: blur(10px);
        z-index: 100;
        transition: opacity 0.3s ease;
      }

      /* 为展开模式优化样式 */
      .expanded-mode {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      }

      .expanded-mode .status-bar {
        bottom: 20px;
        left: 20px;
        right: 20px;
        font-size: 14px;
        padding: 12px 20px;
      }

      .status-bar.hidden {
        opacity: 0;
        pointer-events: none;
      }

      .loading,
      .error {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        text-align: center;
        z-index: 50;
      }

      .loading {
        color: #666;
        font-size: 14px;
      }

      .loading::after {
        content: '';
        display: inline-block;
        width: 16px;
        height: 16px;
        border: 2px solid #ddd;
        border-radius: 50%;
        border-top-color: #007aff;
        animation: spin 1s ease-in-out infinite;
        margin-left: 8px;
      }

      @keyframes spin {
        to {
          transform: rotate(360deg);
        }
      }

      .error {
        color: #ff4757;
        font-size: 12px;
        background: rgba(255, 255, 255, 0.9);
        padding: 20px;
        border-radius: 10px;
        max-width: 80%;
      }

      /* 隐藏滚动条 */
      ::-webkit-scrollbar {
        display: none;
      }
    </style>
  </head>

  <body>
    <!-- 加载提示 -->
    <div class="loading" id="loading">正在加载模型...</div>

    <!-- 状态栏 -->
    <div class="status-bar" id="statusBar"></div>

    <!-- Canvas -->
    <canvas id="canvas"></canvas>

    <!-- 引入必要的库 -->
    <script src="../../../../assets/live2d/libs/live2dcubismcore.min.js"></script>
    <script src="../../../../assets/live2d/libs/live2d.min.js"></script>
    <script src="../../../../../../../node_modules/pixi.js/dist/browser/pixi.min.js"></script>
    <script src="../../../../../../../node_modules/pixi-live2d-display/dist/index.min.js"></script>

    <script>
      console.log('[Live2D HTML] Script started loading')

      // 全局状态
      const state = {
        app: null,
        model: null,
        statusTimeout: null,
        isDragging: false,
        lastMouse: { x: 0, y: 0 },
        modelScale: 1,
        isExpandedMode: false,
        positions: { x: 0, y: 0 },
        baseModelX: 0,
        containerWidth: 200
      }

      // 模型路径
      const modelPath = '../../../../assets/live2d/models/hiyori_pro_zh/runtime/hiyori_pro_t11.model3.json'

      // 状态栏管理
      function showStatus(message, duration = 3000) {
        const statusBar = document.getElementById('statusBar')
        statusBar.textContent = message
        statusBar.classList.remove('hidden')

        clearTimeout(state.statusTimeout)
        if (duration > 0) {
          state.statusTimeout = setTimeout(() => {
            statusBar.classList.add('hidden')
          }, duration)
        }
      }

      // 初始化函数
      async function init() {
        try {
          // 创建 PIXI 应用
          state.app = new PIXI.Application({
            view: document.getElementById('canvas'),
            autoStart: true,
            resizeTo: window,
            backgroundColor: 0x000000,
            backgroundAlpha: 0,
            antialias: true
          })

          // 加载 Live2D 模型
          state.model = await PIXI.live2d.Live2DModel.from(modelPath)

          // 添加模型到舞台
          state.app.stage.addChild(state.model)

          // 设置模型初始属性
          setupModel()

          // 添加交互事件
          setupInteraction()

          // 隐藏加载提示
          document.getElementById('loading').style.display = 'none'

          showStatus('🌸 Hiyori已准备就绪！点击与她互动吧~')

          // 通知父窗口准备就绪
          sendToParent('event', { action: 'ready' })
        } catch (error) {
          console.error('加载模型时出错:', error)
          showError('❌ 模型加载失败<br><small>请检查模型文件路径</small>')

          // 通知父窗口发生错误
          sendToParent('event', { action: 'error', data: error.message })
        }
      }

      // 显示错误信息
      function showError(message) {
        document.getElementById('loading').style.display = 'none'
        const errorDiv = document.createElement('div')
        errorDiv.className = 'error'
        errorDiv.innerHTML = message
        document.body.appendChild(errorDiv)
      }

      // 设置模型属性
      function setupModel() {
        if (!state.model) return

        // 根据窗口大小调整模型缩放
        const baseScale = Math.min(window.innerWidth / 800, window.innerHeight / 600) * 0.4
        state.modelScale = baseScale
        state.model.scale.set(state.modelScale)

        // 设置模型初始位置
        calculateModelPositions()

        // 设置锚点为中心
        state.model.anchor.set(0.5, 0.5)

        // 启用交互
        state.model.interactive = true
        state.model.buttonMode = true
      }

      // 平滑更新模型位置
      function updateModelPosition(targetX, targetY, duration = 300) {
        const start = { x: state.model.x, y: state.model.y }
        const startTime = Date.now()

        function animate() {
          const elapsed = Date.now() - startTime
          const progress = Math.min(elapsed / duration, 1)
          const easeProgress = 1 - Math.pow(1 - progress, 3) // 缓动函数

          state.model.x = start.x + (targetX - start.x) * easeProgress
          state.model.y = start.y + (targetY - start.y) * easeProgress

          if (progress < 1) requestAnimationFrame(animate)
        }

        animate()
      }

      // 计算模型位置
      function calculateModelPositions() {
        if (!state.model) return

        updateModelPosition(state.app.screen.width / 2, state.app.screen.height / 2)
        showStatus(`${state.app.screen.width / 2}, ${state.app.screen.height / 2}`)
      }

      // 设置交互事件
      function setupInteraction() {
        if (!state.model) return

        // 模型点击
        state.model.on('pointerdown', onModelPointerDown)

        // 舞台交互
        state.app.stage.interactive = true
        ;['pointermove', 'pointerup', 'pointerupoutside'].forEach((event) => {
          state.app.stage.on(event, event === 'pointermove' ? onMouseMove : onPointerUp)
        })

        // 滚轮缩放
        state.app.view.addEventListener('wheel', onWheel, { passive: false })

        // 窗口大小改变
        window.addEventListener('resize', calculateModelPositions)
      }

      // 模型指针按下事件（支持拖拽）
      function onModelPointerDown(event) {
        if (!state.model) return

        // 记录拖拽开始位置
        state.isDragging = true
        state.lastMouse.x = event.data.global.x
        state.lastMouse.y = event.data.global.y

        // 播放随机点击动作
        const motionGroups = ['Tap', 'Tap@Body']
        const randomGroup = motionGroups[Math.floor(Math.random() * motionGroups.length)]

        if (state.model.internalModel.motionManager) {
          state.model.internalModel.motionManager.startMotion(randomGroup, 0)
        }

        // 显示随机互动反馈
        const messages = [
          '🌸 你好呀~',
          '✨ 有什么可以帮助你的吗？',
          '💕 很高兴见到你！',
          '🎵 今天心情不错呢~',
          '🌟 让我们一起聊天吧！'
        ]
        showStatus(messages[Math.floor(Math.random() * messages.length)])
      }

      // 指针释放事件
      function onPointerUp() {
        state.isDragging = false
      }

      // 鼠标移动事件（视线跟随和拖拽）
      function onMouseMove(event) {
        if (!state.model?.internalModel) return

        const mouse = { x: event.data.global.x, y: event.data.global.y }

        // 处理拖拽
        if (state.isDragging) {
          const delta = {
            x: mouse.x - state.lastMouse.x,
            y: mouse.y - state.lastMouse.y
          }

          // 更新模型位置
          state.model.x += delta.x
          state.model.y += delta.y

          // 更新鼠标位置
          state.lastMouse = mouse

          showStatus('🖱️ 拖拽中...', 500)
          return
        }

        // 视线跟随（仅在非拖拽状态下）
        const delta = {
          x: (mouse.x - state.model.x) / state.app.screen.width,
          y: (mouse.y - state.model.y) / state.app.screen.height
        }

        // 设置视线参数（如果模型支持）
        try {
          const coreModel = state.model.internalModel.coreModel
          if (coreModel) {
            const params = ['ParamAngleX', 'ParamAngleY']
            const values = [delta.x * 30, delta.y * 30]

            params.forEach((param, i) => {
              const index = coreModel.getParameterIndex(param)
              if (index >= 0) coreModel.setParameterValueByIndex(index, values[i])
            })
          }
        } catch (e) {
          // 忽略参数设置错误
        }
      }

      // 滚轮缩放事件
      function onWheel(event) {
        if (!state.model) return

        event.preventDefault()

        const scaleFactor = event.deltaY > 0 ? 0.9 : 1.1
        const newScale = state.modelScale * scaleFactor
        const [minScale, maxScale] = [0.1, 3.0]

        if (newScale >= minScale && newScale <= maxScale) {
          state.modelScale = newScale
          state.model.scale.set(state.modelScale)

          const scalePercent = Math.round(state.modelScale * 100)
          showStatus(`🔍 缩放: ${scalePercent}%`, 1000)
        }
      }

      // 表情和姿势参数映射
      const parameterMaps = {
        expressions: {
          Default: {},
          Happy: { ParamMouthForm: 1.0, ParamEyeLOpen: 1.0, ParamEyeROpen: 1.0 },
          Sad: { ParamMouthForm: -1.0, ParamEyeLOpen: 0.5, ParamEyeROpen: 0.5 },
          Surprised: { ParamMouthForm: 0.8, ParamEyeLOpen: 1.2, ParamEyeROpen: 1.2 },
          Angry: { ParamMouthForm: -0.8, ParamEyeLOpen: 0.3, ParamEyeROpen: 0.3 },
          Thinking: { ParamEyeLOpen: 0.7, ParamEyeROpen: 0.7 },
          Confused: { ParamMouthForm: 0.3, ParamEyeLOpen: 0.8, ParamEyeROpen: 0.8 },
          Excited: { ParamMouthForm: 1.2, ParamEyeLOpen: 1.3, ParamEyeROpen: 1.3 },
          Sleepy: { ParamEyeLOpen: 0.2, ParamEyeROpen: 0.2 },
          Wink: { ParamEyeLOpen: 0.0, ParamEyeROpen: 1.0 }
        },
        poses: {
          Normal: {},
          Thinking: { ParamAngleX: 5.0, ParamAngleY: -10.0, ParamBodyAngleX: 2.0 },
          Happy: { ParamAngleX: -3.0, ParamAngleY: 5.0, ParamBodyAngleX: -1.0 },
          Surprised: { ParamAngleX: 0.0, ParamAngleY: 0.0, ParamBodyAngleX: 0.0 },
          Greeting: { ParamAngleX: -5.0, ParamAngleY: 8.0, ParamBodyAngleX: -3.0 },
          Relaxed: { ParamAngleX: 2.0, ParamAngleY: -5.0, ParamBodyAngleX: 1.0 },
          Focused: { ParamAngleX: 8.0, ParamAngleY: -15.0, ParamBodyAngleX: 3.0 }
        }
      }

      // 获取参数映射
      function getParameters(type, name) {
        return parameterMaps[type]?.[name] || parameterMaps[type]?.['Default'] || {}
      }

      // 新的通信接口 - 使用事件系统
      function handleReceiveMessage(message) {
        if (!state.model) return

        // 播放说话动作
        if (state.model.internalModel.motionManager) {
          state.model.internalModel.motionManager.startMotion('Idle', 0)
        }

        showStatus(`💬 ${message}`, 5000)
        console.info('[Live2D Communication] Message received', { message })

        // 通知父窗口消息已处理
        sendToParent('event', { action: 'message-processed', data: { message } })
      }

      // 播放动作的通用函数
      function playAnimation(name, type = 'motion') {
        if (!state.model?.internalModel?.motionManager) {
          console.warn(`[Live2D Animation] Cannot play ${type}: model or motionManager not available`, { name })
          return
        }

        try {
          state.model.internalModel.motionManager.startMotion(name, 0)
          const emoji = type === 'motion' ? '🎭' : '🎬'
          showStatus(`${emoji} 播放${type === 'motion' ? '动作' : '动画'}: ${name}`, 2000)
          console.info(`[Live2D Animation] ${type} started successfully`, {
            name,
            timestamp: new Date().toISOString()
          })
        } catch (error) {
          console.error(`[Live2D Animation] Failed to start ${type}`, { name, error })
          showStatus(`❌ ${type === 'motion' ? '动作' : '动画'}播放失败: ${name}`, 2000)
        }
      }

      // 新的动画播放接口
      function handlePlayMotion(motionGroup) {
        playAnimation(motionGroup, 'motion')

        // 通知父窗口动画开始
        sendToParent('event', { action: 'animation-started', data: { type: 'motion', name: motionGroup } })
      }

      function handlePlayAction(actionName) {
        playAnimation(actionName, 'action')

        // 通知父窗口动画开始
        sendToParent('event', { action: 'animation-started', data: { type: 'action', name: actionName } })
      }

      // 设置参数的通用函数
      function setParameters(name, type) {
        if (!state.model?.internalModel?.coreModel) {
          console.warn(`[Live2D Animation] Cannot set ${type}: coreModel not available`, { name })
          return
        }

        try {
          const params = getParameters(type + 's', name)
          const coreModel = state.model.internalModel.coreModel

          Object.entries(params).forEach(([paramName, value]) => {
            const paramIndex = coreModel.getParameterIndex(paramName)
            if (paramIndex >= 0) {
              coreModel.setParameterValue(paramIndex, value)
            }
          })

          const emoji = type === 'expression' ? '😊' : '🎭'
          const label = type === 'expression' ? '表情' : '姿势'
          showStatus(`${emoji} 设置${label}: ${name}`, 2000)
          console.info(`[Live2D Animation] ${type} set successfully`, {
            name,
            parameters: params,
            timestamp: new Date().toISOString()
          })
        } catch (error) {
          const label = type === 'expression' ? '表情' : '姿势'
          console.error(`[Live2D Animation] Failed to set ${type}`, { name, error })
          showStatus(`❌ ${label}设置失败: ${name}`, 2000)
        }
      }

      // 新的参数设置接口
      function handleSetExpression(expression) {
        setParameters(expression, 'expression')

        // 通知父窗口表情设置完成
        sendToParent('event', { action: 'expression-set', data: { expression } })
      }

      function handleSetPose(pose) {
        setParameters(pose, 'pose')

        // 通知父窗口姿势设置完成
        sendToParent('event', { action: 'pose-set', data: { pose } })
      }

      function handleSetExpandedMode(expanded) {
        if (!state.model || state.isExpandedMode === expanded) return

        state.isExpandedMode = expanded
        state.containerWidth = expanded ? state.app.screen.width : 200

        calculateModelPositions()

        // 切换CSS类和状态提示
        document.body.classList.toggle('expanded-mode', expanded)
        showStatus(expanded ? '展开模式 - 模型居中' : '普通模式', 2000)

        console.log('切换展开模式:', expanded, '容器宽度:', state.containerWidth)

        // 通知父窗口模式切换完成
        sendToParent('event', { action: 'expanded-mode-set', data: { expanded } })
      }

      function handleTestAnimations() {
        console.info('[Live2D Test] Starting animation test')

        const tests = [
          { delay: 1000, action: () => handlePlayMotion('Happy'), log: 'Testing action: Happy' },
          { delay: 2000, action: () => handleSetExpression('Happy'), log: 'Testing expression: Happy' },
          { delay: 3000, action: () => handleSetPose('Happy'), log: 'Testing pose: Happy' }
        ]

        tests.forEach(({ delay, action, log }) => {
          setTimeout(() => {
            console.info(`[Live2D Test] ${log}`)
            action()
          }, delay)
        })

        console.info('[Live2D Test] Animation test sequence started')

        // 通知父窗口测试开始
        sendToParent('event', { action: 'test-animations-started' })
      }

      // 简化的通信机制 - 监听来自父窗口的PostMessage
      window.addEventListener('message', (event) => {
        if (event.data && event.data.source === 'live2d-parent') {
          const { action, ...data } = event.data

          switch (action) {
            case 'playMotion':
              if (data.motionName && state.model) {
                try {
                  // 使用正确的Live2D动画播放API
                  if (state.model.internalModel?.motionManager) {
                    state.model.internalModel.motionManager.startMotion(data.motionName, 0)
                    showStatus(`播放动画: ${data.motionName}`)
                    console.log(`播放动画: ${data.motionName}`)
                  } else {
                    console.warn('MotionManager不可用')
                  }
                } catch (error) {
                  console.warn(`动画播放失败: ${data.motionName}`, error)
                }
              }
              break

            case 'setExpanded':
              state.isExpandedMode = data.expanded
              if (data.expanded) {
                document.body.classList.add('expanded-mode')
              } else {
                document.body.classList.remove('expanded-mode')
              }
              calculateModelPositions()
              break

            case 'sendMessage':
              if (data.message) {
                showStatus(data.message, 3000)
              }
              break

            default:
              console.log('未知消息:', event.data)
          }
        }
      })

      // 发送消息到父窗口
      function sendToParent(type, data = {}) {
        if (window.parent && window.parent !== window) {
          window.parent.postMessage(
            {
              source: 'live2d',
              type,
              ...data,
              timestamp: Date.now()
            },
            '*'
          )
        }
      }

      // 事件监听器
      window.addEventListener('load', init)
      window.addEventListener('error', (e) => {
        console.error('页面错误:', e.error)
        showError('❌ 发生错误<br><small>请检查控制台</small>')

        // 通知父窗口发生错误
        sendToParent('event', {
          action: 'error',
          data: {
            message: e.error?.message || '未知错误',
            filename: e.filename,
            lineno: e.lineno,
            colno: e.colno,
            stack: e.error?.stack
          }
        })
      })

      // 定期播放待机动作
      setInterval(() => {
        if (state.model?.internalModel?.motionManager) {
          state.model.internalModel.motionManager.startMotion('Idle', 0)
        }
      }, 30000)
    </script>
  </body>
</html>
