import { loggerService } from '@logger'
import { EventEmitter, EVENT_NAMES } from '@renderer/services/EventService'
import { LIVE2D_CONSTANTS } from '@renderer/services/Live2DService'
import { WebviewTag } from 'electron'
import { FC, useCallback, useEffect, useRef } from 'react'
import styled from 'styled-components'

const logger = loggerService.withContext('Live2DViewer')

/**
 * Live2D 查看器组件属性
 */
export interface Live2DViewerProps {
  /** Live2D 文件 URL */
  fileUrl: string
  /** 是否正在加载 */
  isLoading: boolean
  /** 是否有错误 */
  hasError: boolean
  /** Live2D 就绪回调 */
  onReady?: () => void
  /** Live2D 错误回调 */
  onError?: (error: string) => void
}

/**
 * Live2D 查看器组件
 * 负责 webview 的渲染和与 Live2D 页面的通信
 */
const Live2DViewer: FC<Live2DViewerProps> = ({
  fileUrl,
  isLoading,
  hasError,
  onReady,
  onError
}) => {
  const webviewRef = useRef<WebviewTag>(null)

  // 设置 webview 事件监听
  useEffect(() => {
    const webview = webviewRef.current
    if (!webview || !fileUrl) return

    const handleDomReady = () => {
      logger.info('Live2D webview DOM 就绪')

      // 延迟一段时间确保 Live2D 完全加载
      setTimeout(() => {
        onReady?.()
      }, LIVE2D_CONSTANTS.WEBVIEW_READY_DELAY)
    }

    const handleLoadStart = () => {
      logger.debug('Live2D webview 开始加载')
    }

    const handleLoadStop = () => {
      logger.debug('Live2D webview 加载完成')
    }

    const handleLoadFail = (event: any) => {
      const errorMessage = `Live2D 加载失败: ${event.errorDescription || '未知错误'}`
      logger.error('Live2D webview 加载失败', {
        errorCode: event.errorCode,
        errorDescription: event.errorDescription,
        validatedURL: event.validatedURL
      })
      onError?.(errorMessage)
    }

    const handleConsoleMessage = (event: any) => {
      const { level, message, line, sourceId } = event

      // 根据日志级别记录
      switch (level) {
        case 0: // info
          logger.debug(`Live2D Console: ${message}`, { line, sourceId })
          break
        case 1: // warning
          logger.warn(`Live2D Console Warning: ${message}`, { line, sourceId })
          break
        case 2: // error
          logger.error(`Live2D Console Error: ${message}`, { line, sourceId })
          break
        default:
          logger.debug(`Live2D Console: ${message}`, { level, line, sourceId })
      }
    }

    const handleIpcMessage = (event: any) => {
      const { channel, args } = event
      logger.debug('收到 Live2D IPC 消息', { channel, args })

      // 处理来自 Live2D 页面的消息
      if (channel === 'live2d-message') {
        const [message] = args
        handleLive2DMessage(message)
      }
    }

    // 绑定事件监听器
    webview.addEventListener('dom-ready', handleDomReady)
    webview.addEventListener('did-start-loading', handleLoadStart)
    webview.addEventListener('did-stop-loading', handleLoadStop)
    webview.addEventListener('did-fail-load', handleLoadFail)
    webview.addEventListener('console-message', handleConsoleMessage)
    webview.addEventListener('ipc-message', handleIpcMessage)

    return () => {
      // 清理事件监听器
      webview.removeEventListener('dom-ready', handleDomReady)
      webview.removeEventListener('did-start-loading', handleLoadStart)
      webview.removeEventListener('did-stop-loading', handleLoadStop)
      webview.removeEventListener('did-fail-load', handleLoadFail)
      webview.removeEventListener('console-message', handleConsoleMessage)
      webview.removeEventListener('ipc-message', handleIpcMessage)
    }
  }, [fileUrl, onReady, onError])

  // 处理来自 Live2D 页面的消息
  const handleLive2DMessage = useCallback((message: any) => {
    logger.debug('处理 Live2D 消息', { message })

    switch (message.type) {
      case 'ready':
        logger.info('Live2D 模型加载完成')
        onReady?.()
        break
      case 'error':
        logger.error('Live2D 模型错误', { error: message.error })
        onError?.(message.error || 'Live2D 模型错误')
        break
      case 'animation-complete':
        logger.debug('Live2D 动画完成', { animation: message.animation })
        EventEmitter.emit(EVENT_NAMES.LIVE2D_ANIMATION_COMPLETE, message)
        break
      default:
        logger.debug('未知的 Live2D 消息类型', { type: message.type, message })
    }
  }, [onReady, onError])

  // 发送消息到 Live2D 页面
  const sendMessageToLive2D = useCallback((message: any) => {
    const webview = webviewRef.current
    if (!webview) {
      logger.warn('无法发送消息到 Live2D: webview 未就绪')
      return
    }

    try {
      webview.send('live2d-command', message)
      logger.debug('发送消息到 Live2D', { message })
    } catch (error) {
      logger.error('发送消息到 Live2D 失败', error as Error)
    }
  }, [])

  // 监听 Live2D 事件
  useEffect(() => {
    const handlePlayAnimation = (data: any) => {
      sendMessageToLive2D({
        type: 'play-animation',
        data
      })
    }

    const handleSetExpression = (data: any) => {
      sendMessageToLive2D({
        type: 'set-expression',
        data
      })
    }

    const handleSetPose = (data: any) => {
      sendMessageToLive2D({
        type: 'set-pose',
        data
      })
    }

    // 绑定事件监听
    EventEmitter.on(EVENT_NAMES.LIVE2D_PLAY_ANIMATION, handlePlayAnimation)
    EventEmitter.on(EVENT_NAMES.LIVE2D_SET_EXPRESSION, handleSetExpression)
    EventEmitter.on(EVENT_NAMES.LIVE2D_SET_POSE, handleSetPose)

    return () => {
      // 清理事件监听
      EventEmitter.off(EVENT_NAMES.LIVE2D_PLAY_ANIMATION, handlePlayAnimation)
      EventEmitter.off(EVENT_NAMES.LIVE2D_SET_EXPRESSION, handleSetExpression)
      EventEmitter.off(EVENT_NAMES.LIVE2D_SET_POSE, handleSetPose)
    }
  }, [sendMessageToLive2D])

  if (!fileUrl) {
    return <Container>正在初始化...</Container>
  }

  return (
    <Container>
      <StyledWebview
        ref={webviewRef}
        src={fileUrl}
        partition={LIVE2D_CONSTANTS.WEBVIEW_PARTITION}
        allowpopups="true"
        webpreferences="contextIsolation=false, nodeIntegration=true"
        $isVisible={!isLoading && !hasError}
      />
    </Container>
  )
}

// 样式组件
const Container = styled.div`
  width: 100%;
  height: 100%;
  position: relative;
  overflow: hidden;
  background: transparent;
`

const StyledWebview = styled.webview<{ $isVisible: boolean }>`
  width: 100%;
  height: 100%;
  border: none;
  background: transparent;
  opacity: ${props => props.$isVisible ? 1 : 0};
  transition: opacity 0.3s ease;
  pointer-events: ${props => props.$isVisible ? 'auto' : 'none'};
`

export default Live2DViewer