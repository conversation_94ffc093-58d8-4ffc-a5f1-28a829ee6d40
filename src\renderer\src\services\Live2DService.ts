import { loggerService } from '@logger'
import { EventEmitter, EVENT_NAMES } from './EventService'
import store from '@renderer/store'
import {
  setError,
  setFileUrl,
  setLoading,
  setReady,
  setCurrentAnimation,
  setListening,
  setQueueCount
} from '@renderer/store/virtualCharacter'

const logger = loggerService.withContext('Live2DService')

/**
 * Live2D 业务逻辑服务
 * 采用函数式服务模式，管理 Live2D 的核心业务逻辑
 */

// 类型定义
export interface AppInfo {
  appPath: string
  version: string
  isPackaged: boolean
}

export interface AnimationCommand {
  type: 'action' | 'expression' | 'pose'
  name: string
  priority?: number
}

export interface Live2DMessage {
  type: 'command' | 'event' | 'response'
  action?: string
  data?: any
  timestamp?: number
}

// 常量定义
export const LIVE2D_CONSTANTS = {
  FILE_PATH: '/src/renderer/src/windows/mini/virtualCharacter/live2d/live2d.html',
  WEBVIEW_PARTITION: 'persist:webview',
  ANIMATION_DURATION: 300,
  WEBVIEW_READY_DELAY: 500
} as const

// 动画队列管理
let animationQueue: AnimationCommand[] = []

/**
 * 初始化 Live2D 文件 URL
 */
export const initializeLive2DFileUrl = async (): Promise<string> => {
  try {
    logger.info('开始初始化 Live2D 文件 URL')

    const appInfo: AppInfo = await window.api.getAppInfo()
    const fileUrl = `file://${appInfo.appPath}${LIVE2D_CONSTANTS.FILE_PATH}?appPath=${encodeURIComponent(appInfo.appPath)}`

    store.dispatch(setFileUrl(fileUrl))
    logger.info('Live2D 文件 URL 初始化成功', { fileUrl })

    return fileUrl
  } catch (error) {
    const errorMessage = '无法获取文件路径'
    logger.error('Live2D 文件 URL 初始化失败', error as Error)
    store.dispatch(setError(errorMessage))
    throw new Error(errorMessage)
  }
}

/**
 * 设置 Live2D 加载状态
 */
export const setLive2DLoading = (loading: boolean): void => {
  logger.debug('设置 Live2D 加载状态', { loading })
  store.dispatch(setLoading(loading))
}

/**
 * 设置 Live2D 就绪状态
 */
export const setLive2DReady = (ready: boolean): void => {
  logger.info('设置 Live2D 就绪状态', { ready })
  store.dispatch(setReady(ready))

  if (ready) {
    // 处理队列中的动画
    processAnimationQueue()
    // 发送就绪事件
    EventEmitter.emit(EVENT_NAMES.LIVE2D_READY)
  }
}

/**
 * 设置 Live2D 错误状态
 */
export const setLive2DError = (error: string | null): void => {
  logger.error('Live2D 错误', { error })
  store.dispatch(setError(error))

  if (error) {
    EventEmitter.emit(EVENT_NAMES.LIVE2D_ERROR, { error })
  }
}

/**
 * 播放 Live2D 动画
 */
export const playLive2DAnimation = (animationName: string, priority: number = 1): void => {
  logger.debug('请求播放 Live2D 动画', { animationName, priority })

  const command: AnimationCommand = {
    type: 'action',
    name: animationName,
    priority
  }

  const state = store.getState().virtualCharacter

  if (state.isReady) {
    // 直接播放动画
    executeAnimationCommand(command)
  } else {
    // 添加到队列
    addToAnimationQueue(command)
  }

  store.dispatch(setCurrentAnimation(animationName))
  EventEmitter.emit(EVENT_NAMES.LIVE2D_PLAY_ANIMATION, { animationName, priority })
}

/**
 * 设置 Live2D 表情
 */
export const setLive2DExpression = (expressionName: string): void => {
  logger.debug('设置 Live2D 表情', { expressionName })

  const command: AnimationCommand = {
    type: 'expression',
    name: expressionName
  }

  const state = store.getState().virtualCharacter

  if (state.isReady) {
    executeAnimationCommand(command)
  } else {
    addToAnimationQueue(command)
  }

  EventEmitter.emit(EVENT_NAMES.LIVE2D_SET_EXPRESSION, { expressionName })
}

/**
 * 设置 Live2D 姿势
 */
export const setLive2DPose = (poseName: string): void => {
  logger.debug('设置 Live2D 姿势', { poseName })

  const command: AnimationCommand = {
    type: 'pose',
    name: poseName
  }

  const state = store.getState().virtualCharacter

  if (state.isReady) {
    executeAnimationCommand(command)
  } else {
    addToAnimationQueue(command)
  }

  EventEmitter.emit(EVENT_NAMES.LIVE2D_SET_POSE, { poseName })
}

/**
 * 发送消息到 Live2D
 */
export const sendMessageToLive2D = (message: string): void => {
  logger.debug('发送消息到 Live2D', { message })

  const state = store.getState().virtualCharacter

  if (state.isReady) {
    // 这里可以根据消息内容触发相应的动画
    // 例如：根据消息情感分析选择合适的表情和动作
    playLive2DAnimation('Talk', 2)
  }

  EventEmitter.emit(EVENT_NAMES.LIVE2D_SEND_MESSAGE, { message })
}

/**
 * 添加动画到队列
 */
const addToAnimationQueue = (command: AnimationCommand): void => {
  animationQueue.push(command)
  store.dispatch(setQueueCount(animationQueue.length))
  logger.debug('动画添加到队列', { command, queueLength: animationQueue.length })
}

/**
 * 处理动画队列
 */
const processAnimationQueue = (): void => {
  if (animationQueue.length === 0) return

  logger.info('开始处理动画队列', { queueLength: animationQueue.length })

  // 按优先级排序（高优先级先执行）
  animationQueue.sort((a, b) => (b.priority || 1) - (a.priority || 1))

  // 执行队列中的动画
  while (animationQueue.length > 0) {
    const command = animationQueue.shift()
    if (command) {
      executeAnimationCommand(command)
    }
  }

  store.dispatch(setQueueCount(0))
  logger.info('动画队列处理完成')
}

/**
 * 执行动画命令
 */
const executeAnimationCommand = (command: AnimationCommand): void => {
  logger.debug('执行动画命令', { command })

  // 这里需要通过 webview 发送消息到 Live2D 页面
  // 具体实现将在组件中处理
  EventEmitter.emit(EVENT_NAMES.LIVE2D_PLAY_ANIMATION, {
    type: command.type,
    name: command.name,
    priority: command.priority
  })
}

/**
 * 开始监听 AI 回复
 */
export const startListeningToAI = (): void => {
  logger.info('开始监听 AI 回复')
  store.dispatch(setListening(true))

  // 监听 AI 回复完成事件
  EventEmitter.on(EVENT_NAMES.MESSAGE_COMPLETE, handleAIResponse)
}

/**
 * 停止监听 AI 回复
 */
export const stopListeningToAI = (): void => {
  logger.info('停止监听 AI 回复')
  store.dispatch(setListening(false))

  // 移除事件监听
  EventEmitter.off(EVENT_NAMES.MESSAGE_COMPLETE, handleAIResponse)
}

/**
 * 处理 AI 回复
 */
const handleAIResponse = (data: any): void => {
  logger.debug('收到 AI 回复', { data })

  // 根据 AI 回复触发相应的动画
  // 这里可以根据回复内容的情感、长度等因素选择合适的动画
  playLive2DAnimation('Happy', 3)

  // 发送 AI 回复事件
  EventEmitter.emit(EVENT_NAMES.AI_RESPONSE_RECEIVED, data)
}

/**
 * 重置 Live2D 状态
 */
export const resetLive2DState = (): void => {
  logger.info('重置 Live2D 状态')

  // 清空动画队列
  animationQueue = []

  // 停止监听
  stopListeningToAI()

  // 重置 Redux 状态
  store.dispatch(setLoading(true))
  store.dispatch(setReady(false))
  store.dispatch(setError(null))
  store.dispatch(setCurrentAnimation(null))
  store.dispatch(setQueueCount(0))
}

/**
 * 获取当前 Live2D 状态
 */
export const getLive2DState = () => {
  return store.getState().virtualCharacter
}

/**
 * 重试加载 Live2D
 */
export const retryLive2DLoad = async (): Promise<void> => {
  logger.info('重试加载 Live2D')

  // 重置状态
  resetLive2DState()

  try {
    // 重新初始化文件 URL
    await initializeLive2DFileUrl()
  } catch (error) {
    logger.error('重试加载 Live2D 失败', error as Error)
    throw error
  }
}