import { CompressOutlined, ExpandOutlined } from '@ant-design/icons'
import { loggerService } from '@logger'
import { EVENT_NAMES, EventEmitter } from '@renderer/services/EventService'
import {
  initializeLive2DFileUrl,
  LIVE2D_CONSTANTS,
  retryLive2DLoad,
  setLive2DError,
  setLive2DLoading,
  setLive2DReady,
  startListeningToAI,
  stopListeningToAI
} from '@renderer/services/Live2DService'
import { useAppDispatch, useAppSelector } from '@renderer/store'
import { selectIsExpanded, setExpanded } from '@renderer/store/virtualCharacter'
import { Button } from 'antd'
import { FC, useCallback, useEffect, useRef } from 'react'
import styled from 'styled-components'

const logger = loggerService.withContext('VirtualCharacter')

/**
 * Live2D 虚拟角色组件属性
 */
export interface VirtualCharacterProps {
  /** 是否展开（外部控制） */
  isExpanded?: boolean
  /** 展开状态变化回调 */
  onToggleExpand?: (expanded: boolean) => void
}

/**
 * Live2D 虚拟角色主组件
 * 重构后的版本，专注于 UI 展示和用户交互
 */
const VirtualCharacter: FC<VirtualCharacterProps> = ({ isExpanded: propIsExpanded, onToggleExpand }) => {
  const dispatch = useAppDispatch()
  const isExpanded = useAppSelector(selectIsExpanded)
  const virtualCharacterState = useAppSelector((state) => state.virtualCharacter)
  const webviewRef = useRef<Electron.WebviewTag>(null)

  const { isLoading, error, fileUrl, isReady } = virtualCharacterState

  // 初始化 Live2D
  useEffect(() => {
    const initLive2D = async () => {
      try {
        setLive2DLoading(true)
        await initializeLive2DFileUrl()
        logger.info('Live2D 初始化完成')
      } catch (error) {
        logger.error('Live2D 初始化失败', error as Error)
        setLive2DError('Live2D 初始化失败')
      }
    }

    initLive2D()
  }, [])

  // 同步外部传入的展开状态
  useEffect(() => {
    if (propIsExpanded !== undefined && propIsExpanded !== isExpanded) {
      dispatch(setExpanded(propIsExpanded))
    }
  }, [propIsExpanded, isExpanded, dispatch])

  // 监听 AI 回复
  useEffect(() => {
    if (isReady && isExpanded) {
      startListeningToAI()
      logger.info('开始监听 AI 回复')
    } else {
      stopListeningToAI()
      logger.info('停止监听 AI 回复')
    }

    return () => {
      stopListeningToAI()
    }
  }, [isReady, isExpanded])

  // 处理展开/收起切换
  const handleToggleExpand = useCallback(() => {
    const newExpanded = !isExpanded
    dispatch(setExpanded(newExpanded))
    onToggleExpand?.(newExpanded)

    logger.info('Live2D 虚拟角色展开状态切换', {
      from: isExpanded,
      to: newExpanded
    })
  }, [isExpanded, dispatch, onToggleExpand])

  // 处理错误重试
  const handleRetry = useCallback(async () => {
    logger.info('Live2D 虚拟角色重试加载')
    try {
      await retryLive2DLoad()
    } catch (error) {
      logger.error('重试加载失败', error as Error)
    }
  }, [])

  // 处理错误忽略
  const handleDismissError = useCallback(() => {
    logger.info('Live2D 虚拟角色忽略错误')
    setLive2DError(null)
  }, [])

  // Live2D 就绪回调
  const handleLive2DReady = useCallback(() => {
    logger.info('Live2D 就绪')
    setLive2DReady(true)
  }, [])

  // Live2D 错误回调
  const handleLive2DError = useCallback((errorMessage: string) => {
    logger.error('Live2D 错误', { error: errorMessage })
    setLive2DError(errorMessage)
  }, [])

  // Webview 事件处理
  useEffect(() => {
    const webview = webviewRef.current
    if (!webview) return

    const handleWebviewReady = () => {
      logger.info('Live2D webview DOM 就绪')
      handleLive2DReady()
    }

    const handleWebviewLoadFail = (event: any) => {
      logger.error('Live2D webview 加载失败', event)
      handleLive2DError(`Live2D 加载失败: ${event.errorDescription}`)
    }

    const handleWebviewConsoleMessage = (event: any) => {
      if (event.level >= 2) {
        // 错误级别
        logger.error('Live2D Console Error:', event.message, event)
      }
    }

    const handleWebviewIpcMessage = (event: any) => {
      const { channel, args } = event
      if (channel === 'live2d-message') {
        EventEmitter.emit(EVENT_NAMES.LIVE2D_READY, args[0])
      }
    }

    webview.addEventListener('dom-ready', handleWebviewReady)
    webview.addEventListener('did-fail-load', handleWebviewLoadFail)
    webview.addEventListener('console-message', handleWebviewConsoleMessage)
    webview.addEventListener('ipc-message', handleWebviewIpcMessage)

    return () => {
      webview.removeEventListener('dom-ready', handleWebviewReady)
      webview.removeEventListener('did-fail-load', handleWebviewLoadFail)
      webview.removeEventListener('console-message', handleWebviewConsoleMessage)
      webview.removeEventListener('ipc-message', handleWebviewIpcMessage)
    }
  }, [handleLive2DReady, handleLive2DError])

  logger.debug('VirtualCharacter 渲染', {
    isExpanded,
    isLoading,
    hasError: !!error,
    isReady,
    hasFileUrl: !!fileUrl
  })

  return (
    <Container $isExpanded={isExpanded}>
      {/* 控制按钮 */}
      <ExpandButton
        type="text"
        icon={isExpanded ? <CompressOutlined /> : <ExpandOutlined />}
        onClick={handleToggleExpand}
        title={isExpanded ? '收起虚拟角色' : '展开虚拟角色'}
        size="small"
      />

      {/* 加载状态 */}
      {isLoading && (
        <LoadingOverlay>
          <LoadingSpinner />
        </LoadingOverlay>
      )}

      {/* 错误状态 */}
      {error && (
        <ErrorOverlay>
          <ErrorContent>
            <ErrorIcon>❌</ErrorIcon>
            <ErrorTitle>加载失败</ErrorTitle>
            <ErrorMessage>{typeof error === 'string' ? error : String(error)}</ErrorMessage>
            <ErrorActions>
              <Button type="primary" size="small" onClick={handleRetry}>
                重试
              </Button>
              <Button size="small" onClick={handleDismissError}>
                关闭
              </Button>
            </ErrorActions>
          </ErrorContent>
        </ErrorOverlay>
      )}

      {/* Live2D 显示器 */}
      <WebviewContainer>
        <webview
          ref={webviewRef}
          src={fileUrl}
          partition={LIVE2D_CONSTANTS.WEBVIEW_PARTITION}
          {...({ allowpopups: true } as any)}
          webpreferences="contextIsolation=false, nodeIntegration=true"
          style={{
            width: '100%',
            height: '100%',
            border: 'none',
            background: 'transparent',
            opacity: !isLoading && !error ? 1 : 0,
            transition: 'opacity 0.3s ease',
            pointerEvents: !isLoading && !error ? 'auto' : 'none'
          }}
        />
      </WebviewContainer>
    </Container>
  )
}

// 样式组件
const Container = styled.div<{ $isExpanded: boolean }>`
  position: relative;
  width: ${(props) => (props.$isExpanded ? '100%' : '200px')};
  height: 100%;
  background: transparent;
  transition: width 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  -webkit-app-region: no-drag;

  /* 展开时的样式 */
  ${(props) =>
    props.$isExpanded &&
    `
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1000;
    width: 100% !important;
  `}
`

const ExpandButton = styled(Button)`
  position: absolute;
  top: 8px;
  right: 8px;
  z-index: 100;
  background: rgba(0, 0, 0, 0.1) !important;
  border: 1px solid rgba(255, 255, 255, 0.1) !important;
  color: rgba(255, 255, 255, 0.7) !important;
  border-radius: 8px !important;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  -webkit-app-region: no-drag;
  opacity: 0.8;

  &:hover {
    background: rgba(0, 0, 0, 0.2) !important;
    color: rgba(255, 255, 255, 0.9) !important;
    border-color: rgba(255, 255, 255, 0.2) !important;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
    opacity: 1;
  }

  &:active {
    background: rgba(0, 0, 0, 0.15) !important;
    color: rgba(255, 255, 255, 0.8) !important;
    border-color: rgba(255, 255, 255, 0.15) !important;
    transform: translateY(0);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    opacity: 0.9;
  }

  &:focus {
    outline: none !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
  }
`

const WebviewContainer = styled.div`
  width: 100%;
  height: 100%;
  position: relative;
  overflow: hidden;
  background: transparent;
`

const LoadingOverlay = styled.div`
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--color-background);
  z-index: 10;
  -webkit-app-region: no-drag;
`

const LoadingSpinner = styled.div`
  width: 20px;
  height: 20px;
  border: 2px solid var(--color-border);
  border-top: 2px solid var(--color-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;

  @keyframes spin {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }
`

const ErrorOverlay = styled.div`
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--color-background);
  z-index: 10;
  padding: 20px;
  -webkit-app-region: no-drag;
`

const ErrorContent = styled.div`
  text-align: center;
  max-width: 80%;
`

const ErrorIcon = styled.div`
  font-size: 24px;
  margin-bottom: 8px;
`

const ErrorTitle = styled.h4`
  color: var(--color-text);
  margin-bottom: 8px;
  font-size: 14px;
  font-weight: 500;
`

const ErrorMessage = styled.div`
  color: var(--color-error);
  font-size: 12px;
  line-height: 1.4;
  margin-bottom: 12px;
  word-break: break-word;
`

const ErrorActions = styled.div`
  display: flex;
  justify-content: center;
  gap: 8px;
`

export default VirtualCharacter
